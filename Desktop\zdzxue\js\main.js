// 全局用户数据存储
const users = [
    {
        username: 'admin',
        password: 'admin123',
        uid: 'UID_123456',
        avatar: 'images/avatar.png'
    },
    {
        username: 'test',
        password: 'test123',
        uid: 'UID_654321',
        avatar: 'images/avatar.png'
    }
];

// 当前登录用户
let currentUser = null;

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    // 欢迎语打字机效果
    displayWelcomeMessage();

    // 轮播图功能
    initCarousel();

    // 新闻过滤功能
    initNewsFilter();

    // 登录弹窗功能
    initLoginModal();

    // 初始隐藏头像
    const avatarContainer = document.getElementById('avatarContainer');
    if (avatarContainer) {
        avatarContainer.style.display = 'none';
    }
});

/**
 * 根据当前时间显示不同的欢迎语，并添加打字机效果
 */
function displayWelcomeMessage() {
    const welcomeText = document.getElementById('welcomeText');
    if (!welcomeText) return;

    // 获取当前小时
    const currentHour = new Date().getHours();

    // 根据时间段设置不同的欢迎语
    let greeting = '';
    if (currentHour >= 0 && currentHour < 6) {
        greeting = "天黑了，多休息，同学";
    } else if (currentHour >= 6 && currentHour < 11) {
        greeting = "上午好，同学";
    } else if (currentHour >= 11 && currentHour < 13) {
        greeting = "中午好，同学";
    } else if (currentHour >= 13 && currentHour < 20) {
        greeting = "下午好，同学";
    } else {
        greeting = "晚上好，同学";
    }

    // 打字机效果
    let i = 0;
    welcomeText.textContent = ''; // 清空内容

    function typeWriter() {
        if (i < greeting.length) {
            welcomeText.textContent += greeting.charAt(i);
            i++;
            setTimeout(typeWriter, 100); // 每个字符之间的延迟
        }
    }

    // 开始打字效果
    typeWriter();
}

/**
 * 初始化轮播图功能
 */
function initCarousel() {
    const slides = document.querySelectorAll('.carousel-slide');
    const indicators = document.querySelectorAll('.indicator');
    const prevBtn = document.querySelector('.prev-btn');
    const nextBtn = document.querySelector('.next-btn');

    if (!slides.length || !indicators.length) return;

    let currentIndex = 0;
    let interval;

    // 显示指定索引的幻灯片
    function showSlide(index) {
        // 隐藏所有幻灯片
        slides.forEach(slide => {
            slide.classList.remove('active');
        });

        // 移除所有指示器的活动状态
        indicators.forEach(indicator => {
            indicator.classList.remove('active');
        });

        // 显示当前幻灯片和指示器
        slides[index].classList.add('active');
        indicators[index].classList.add('active');

        // 更新当前索引
        currentIndex = index;
    }

    // 显示下一张幻灯片
    function nextSlide() {
        let nextIndex = currentIndex + 1;
        if (nextIndex >= slides.length) {
            nextIndex = 0;
        }
        showSlide(nextIndex);
    }

    // 显示上一张幻灯片
    function prevSlide() {
        let prevIndex = currentIndex - 1;
        if (prevIndex < 0) {
            prevIndex = slides.length - 1;
        }
        showSlide(prevIndex);
    }

    // 开始自动轮播
    function startAutoSlide() {
        interval = setInterval(nextSlide, 5000); // 每5秒切换一次
    }

    // 停止自动轮播
    function stopAutoSlide() {
        clearInterval(interval);
    }

    // 为指示器添加点击事件
    indicators.forEach((indicator, index) => {
        indicator.addEventListener('click', () => {
            showSlide(index);
            stopAutoSlide();
            startAutoSlide();
        });
    });

    // 为上一张/下一张按钮添加点击事件
    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            prevSlide();
            stopAutoSlide();
            startAutoSlide();
        });
    }

    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            nextSlide();
            stopAutoSlide();
            startAutoSlide();
        });
    }

    // 鼠标悬停时停止自动轮播，移开时继续
    const carouselContainer = document.querySelector('.carousel-container');
    if (carouselContainer) {
        carouselContainer.addEventListener('mouseenter', stopAutoSlide);
        carouselContainer.addEventListener('mouseleave', startAutoSlide);
    }

    // 开始自动轮播
    startAutoSlide();
}

/**
 * 初始化新闻过滤功能
 */
function initNewsFilter() {
    const filterBtns = document.querySelectorAll('.filter-btn');
    const newsCards = document.querySelectorAll('.news-card');

    if (!filterBtns.length || !newsCards.length) return;

    // 为过滤按钮添加点击事件
    filterBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            // 移除所有按钮的活动状态
            filterBtns.forEach(b => b.classList.remove('active'));

            // 添加当前按钮的活动状态
            btn.classList.add('active');

            // 获取过滤类别
            const filter = btn.getAttribute('data-filter');

            // 显示或隐藏新闻卡片
            newsCards.forEach(card => {
                if (filter === 'all' || card.getAttribute('data-category') === filter) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    });
}

// 添加视差滚动效果
window.addEventListener('scroll', function() {
    const scrollPosition = window.pageYOffset;

    // 欢迎语部分的视差效果
    const welcomeSection = document.querySelector('.welcome-section');
    if (welcomeSection) {
        welcomeSection.style.backgroundPositionY = scrollPosition * 0.2 + 'px';
    }

    // 学校简介部分的视差效果
    const introSection = document.querySelector('.intro-section');
    if (introSection) {
        introSection.style.backgroundPositionY = -scrollPosition * 0.1 + 'px';
    }
});



/**
 * 初始化登录弹窗功能
 */
function initLoginModal() {
    // 获取DOM元素
    const loginBtn = document.querySelector('.login-btn');
    const userAvatar = document.getElementById('userAvatar');
    const modalOverlay = document.getElementById('loginModal');
    const closeModalBtn = document.getElementById('closeModal');
    const loginTab = document.getElementById('loginTab');
    const registerTab = document.getElementById('registerTab');
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');
    const switchToRegister = document.getElementById('switchToRegister');
    const switchToLogin = document.getElementById('switchToLogin');
    const loginButton = document.getElementById('loginButton');
    const registerButton = document.getElementById('registerButton');
    const loginNotification = document.getElementById('loginNotification');
    const registerNotification = document.getElementById('registerNotification');
    const modalTitle = document.getElementById('modalTitle');

    // 初始化个人信息弹窗
    initProfileModal();

    // 打开登录弹窗
    loginBtn.addEventListener('click', function() {
        // 如果已登录，则不打开登录弹窗
        if (currentUser) return;

        modalOverlay.classList.add('active');
        document.body.style.overflow = 'hidden'; // 防止背景滚动
    });

    // 关闭登录弹窗
    closeModalBtn.addEventListener('click', function() {
        modalOverlay.classList.remove('active');
        document.body.style.overflow = ''; // 恢复背景滚动
        resetForms();
    });

    // 点击弹窗外部关闭弹窗
    modalOverlay.addEventListener('click', function(e) {
        if (e.target === modalOverlay) {
            modalOverlay.classList.remove('active');
            document.body.style.overflow = '';
            resetForms();
        }
    });

    // 切换到登录标签
    loginTab.addEventListener('click', function() {
        loginTab.classList.add('active');
        registerTab.classList.remove('active');
        loginForm.classList.add('active');
        registerForm.classList.remove('active');
        modalTitle.textContent = '用户登录';
        resetNotifications();
    });

    // 切换到注册标签
    registerTab.addEventListener('click', function() {
        registerTab.classList.add('active');
        loginTab.classList.remove('active');
        registerForm.classList.add('active');
        loginForm.classList.remove('active');
        modalTitle.textContent = '用户注册';
        resetNotifications();
    });

    // 从登录表单切换到注册表单
    switchToRegister.addEventListener('click', function(e) {
        e.preventDefault();
        registerTab.click();
    });

    // 从注册表单切换到登录表单
    switchToLogin.addEventListener('click', function(e) {
        e.preventDefault();
        loginTab.click();
    });

    // 登录按钮点击事件
    loginButton.addEventListener('click', function() {
        const username = document.getElementById('loginUsername').value.trim();
        const password = document.getElementById('loginPassword').value.trim();

        // 验证输入
        if (!username || !password) {
            showNotification(loginNotification, '请输入用户名和密码', 'error');
            return;
        }

        // 验证用户
        const user = users.find(u => u.username === username);

        if (!user) {
            showNotification(loginNotification, '用户不存在，是否需要注册新账号？', 'error');
            // 添加一个确认按钮
            loginNotification.innerHTML += ' <button id="confirmRegister" class="register-link" style="background:none;border:none;cursor:pointer;padding:0;">点击注册</button>';

            // 为确认按钮添加事件
            document.getElementById('confirmRegister').addEventListener('click', function() {
                registerTab.click();
                document.getElementById('registerUsername').value = username;
                document.getElementById('registerPassword').focus();
            });
            return;
        }

        if (user.password !== password) {
            showNotification(loginNotification, '密码错误，请重试', 'error');
            return;
        }

        // 登录成功
        showNotification(loginNotification, '登录成功！', 'success');

        // 设置当前用户
        currentUser = user;

        // 模拟登录成功后的操作
        setTimeout(function() {
            modalOverlay.classList.remove('active');
            document.body.style.overflow = '';
            resetForms();

            // 更新用户头像和登录按钮
            updateUserInterface();
        }, 1500);
    });

    // 注册按钮点击事件
    registerButton.addEventListener('click', function() {
        const username = document.getElementById('registerUsername').value.trim();
        const password = document.getElementById('registerPassword').value.trim();
        const confirmPassword = document.getElementById('confirmPassword').value.trim();

        // 验证输入
        if (!username || !password || !confirmPassword) {
            showNotification(registerNotification, '请填写所有必填字段', 'error');
            return;
        }

        // 验证密码长度
        if (password.length < 6) {
            showNotification(registerNotification, '密码长度至少为6个字符', 'error');
            return;
        }

        // 验证两次密码是否一致
        if (password !== confirmPassword) {
            showNotification(registerNotification, '两次输入的密码不一致', 'error');
            return;
        }

        // 检查用户名是否已存在
        if (users.some(u => u.username === username)) {
            showNotification(registerNotification, '用户名已存在，请选择其他用户名', 'error');
            return;
        }

        // 生成唯一用户ID
        const uid = 'UID_' + generateUID();

        // 注册成功，添加新用户
        const newUser = {
            username,
            password,
            uid,
            avatar: 'images/avatar.png'
        };

        users.push(newUser);

        // 显示成功消息
        showNotification(registerNotification, '注册成功！正在跳转到登录页面...', 'success');

        // 模拟注册成功后的操作
        setTimeout(function() {
            loginTab.click();
            document.getElementById('loginUsername').value = username;
            document.getElementById('loginPassword').value = password;
        }, 1500);
    });

    // 点击头像打开个人信息弹窗
    userAvatar.addEventListener('click', function() {
        // 如果未登录，则打开登录弹窗
        if (!currentUser) {
            modalOverlay.classList.add('active');
            document.body.style.overflow = 'hidden';
            return;
        }

        // 如果已登录，则打开个人信息弹窗
        const profileModal = document.getElementById('profileModal');
        const profileUsername = document.getElementById('profileUsername');
        const userUniqueId = document.getElementById('userUniqueId');
        const currentAvatarImg = document.getElementById('currentAvatar');
        const profileNotification = document.getElementById('profileNotification');

        // 填充用户信息
        profileUsername.value = currentUser.username;
        userUniqueId.textContent = currentUser.uid;
        currentAvatarImg.src = currentUser.avatar;

        // 重置通知
        profileNotification.className = 'notification';

        // 显示弹窗
        profileModal.classList.add('active');
        document.body.style.overflow = 'hidden';
    });

    // 显示通知消息
    function showNotification(element, message, type) {
        element.textContent = message;
        element.className = 'notification ' + type;
    }

    // 重置通知消息
    function resetNotifications() {
        loginNotification.className = 'notification';
        registerNotification.className = 'notification';
    }

    // 重置表单
    function resetForms() {
        document.getElementById('loginUsername').value = '';
        document.getElementById('loginPassword').value = '';
        document.getElementById('registerUsername').value = '';
        document.getElementById('registerPassword').value = '';
        document.getElementById('confirmPassword').value = '';
        resetNotifications();
    }
}

/**
 * 更新用户界面（登录后）
 */
function updateUserInterface() {
    if (!currentUser) return;

    const loginBtn = document.getElementById('loginBtn');
    const avatarContainer = document.getElementById('avatarContainer');
    const userAvatar = document.getElementById('userAvatar');

    // 隐藏登录按钮
    loginBtn.style.display = 'none';

    // 显示头像
    avatarContainer.style.display = 'block';

    // 更新头像
    userAvatar.src = currentUser.avatar;
}

/**
 * 初始化个人信息弹窗功能
 */
function initProfileModal() {
    const profileModal = document.getElementById('profileModal');
    const closeProfileModalBtn = document.getElementById('closeProfileModal');
    const profileUsername = document.getElementById('profileUsername');
    const currentAvatarImg = document.getElementById('currentAvatar');
    const avatarUpload = document.getElementById('avatarUpload');
    const saveProfileButton = document.getElementById('saveProfileButton');
    const logoutButton = document.getElementById('logoutButton');
    const profileNotification = document.getElementById('profileNotification');

    // 关闭个人信息弹窗
    closeProfileModalBtn.addEventListener('click', function() {
        profileModal.classList.remove('active');
        document.body.style.overflow = '';
    });

    // 点击弹窗外部关闭弹窗
    profileModal.addEventListener('click', function(e) {
        if (e.target === profileModal) {
            profileModal.classList.remove('active');
            document.body.style.overflow = '';
        }
    });

    // 头像上传功能
    avatarUpload.addEventListener('change', function(e) {
        const file = e.target.files[0];

        if (!file) return;

        // 验证文件类型
        if (!file.type.match('image.*')) {
            showProfileNotification('请选择图片文件', 'error');
            return;
        }

        // 验证文件大小（最大2MB）
        if (file.size > 2 * 1024 * 1024) {
            showProfileNotification('图片大小不能超过2MB', 'error');
            return;
        }

        // 读取文件并预览
        const reader = new FileReader();
        reader.onload = function(e) {
            currentAvatarImg.src = e.target.result;
        };
        reader.readAsDataURL(file);
    });

    // 保存个人信息
    saveProfileButton.addEventListener('click', function() {
        if (!currentUser) return;

        const newUsername = profileUsername.value.trim();

        // 验证用户名
        if (!newUsername) {
            showProfileNotification('用户名不能为空', 'error');
            return;
        }

        // 检查用户名是否已存在（排除当前用户）
        if (newUsername !== currentUser.username && users.some(u => u.username === newUsername)) {
            showProfileNotification('用户名已存在，请选择其他用户名', 'error');
            return;
        }

        // 更新用户信息
        currentUser.username = newUsername;
        currentUser.avatar = currentAvatarImg.src;

        // 更新界面
        updateUserInterface();

        // 显示成功消息
        showProfileNotification('个人信息更新成功！', 'success');

        // 3秒后关闭弹窗
        setTimeout(function() {
            profileModal.classList.remove('active');
            document.body.style.overflow = '';
        }, 3000);
    });

    // 退出登录功能
    logoutButton.addEventListener('click', function() {
        // 显示确认消息
        showProfileNotification('正在退出登录...', 'success');

        // 1秒后执行退出登录操作
        setTimeout(function() {
            // 清除当前用户
            currentUser = null;

            // 关闭个人信息弹窗
            profileModal.classList.remove('active');
            document.body.style.overflow = '';

            // 重置界面
            resetUserInterface();
        }, 1000);
    });

    // 显示个人信息弹窗通知
    function showProfileNotification(message, type) {
        profileNotification.textContent = message;
        profileNotification.className = 'notification ' + type;
    }



/**
 * 重置用户界面（退出登录后）
 */
function resetUserInterface() {
    const loginBtn = document.getElementById('loginBtn');
    const avatarContainer = document.getElementById('avatarContainer');
    const userAvatar = document.getElementById('userAvatar');
    const loginNotification = document.getElementById('loginNotification');

    // 显示登录按钮
    loginBtn.style.display = 'block';
    loginBtn.textContent = '登录';
    loginBtn.style.backgroundColor = '';
    loginBtn.style.color = '';

    // 隐藏头像
    avatarContainer.style.display = 'none';

    // 重置头像
    userAvatar.src = 'images/avatar.png';

    // 清除登录成功提示
    loginNotification.className = 'notification';
    loginNotification.textContent = '';
}
}

